import React from 'react';
import { LocalAttachment } from '@/lib/types';
import { X, FileText, Image as ImageIcon } from 'lucide-react'; // Using ImageIcon for clarity
import { Button } from './ui/button';

interface AttachmentPillProps {
  attachment: LocalAttachment;
  onRemove: (attachmentId: string) => void;
}

export const AttachmentPill: React.FC<AttachmentPillProps> = ({ attachment, onRemove }) => {
  const isImage = attachment.type.startsWith('image/') && attachment.dataUrl;
  const isText = attachment.type.startsWith('text/') && attachment.textContent;

  return (
    <div className="inline-flex items-center bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm mr-2 mb-2 shadow">
      {isImage ? (
        <ImageIcon size={16} className="mr-2 text-gray-600 dark:text-gray-400" />
      ) : (
        <FileText size={16} className="mr-2 text-gray-600 dark:text-gray-400" />
      )}
      
      <span className="font-medium truncate max-w-[150px] sm:max-w-[200px] md:max-w-[250px]" title={attachment.name}>
        {attachment.name}
      </span>
      
      <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
        ({Math.round(attachment.size / 1024)} KB)
      </span>

      {isImage && attachment.dataUrl && (
        <img 
          src={attachment.dataUrl} 
          alt={attachment.name} 
          className="w-8 h-8 object-cover rounded ml-2 border border-gray-300 dark:border-gray-600" 
        />
      )}

      {/* In Phase 1, textContent is not directly previewed in the pill, but we could add a small snippet or icon */}

      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6 ml-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
        onClick={() => onRemove(attachment.id)}
        aria-label={`Remove ${attachment.name}`}
      >
        <X size={14} className="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400" />
      </Button>
    </div>
  );
};