import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useApp } from '@/lib/app-context';
import { Send, Plus } from 'lucide-react';
import SimpleMDEEditor from 'react-simplemde-editor';
import 'easymde/dist/easymde.min.css';
import type { Options } from 'easymde';
import EasyMDE from 'easymde';
import { LocalAttachment } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';
import { AttachmentPill } from './AttachmentPill'; // Import AttachmentPill

interface MessageInputProps {
  threadId?: string;
  placeholder?: string;
  autoFocus?: boolean;
  topicId?: string;
  disabled?: boolean;
}

export const MessageInput = ({
  threadId,
  placeholder = "Type a message...",
  autoFocus = false,
  topicId,
  disabled = false
}: MessageInputProps) => {
  const [message, setMessage] = useState('');
  const [pendingAttachments, setPendingAttachments] = useState<LocalAttachment[]>([]);
  const { sendMessage, currentChannel, currentDirectMessage } = useApp();
  const editorInstanceRef = useRef<EasyMDE | null>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const addFileAsAttachment = async (file: File) => {
    const localId = uuidv4();
    const newAttachmentBase: Omit<LocalAttachment, 'dataUrl' | 'textContent'> = {
      id: localId,
      name: file.name,
      type: file.type,
      size: file.size,
      fileObject: file,
    };

    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPendingAttachments(prev => [...prev, { ...newAttachmentBase, dataUrl: e.target?.result as string }]);
      };
      reader.readAsDataURL(file);
    } else if (file.type.startsWith('text/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPendingAttachments(prev => [...prev, { ...newAttachmentBase, textContent: e.target?.result as string }]);
      };
      reader.readAsText(file);
    } else {
      console.warn(`File type ${file.type} not directly supported for inline content. Will be handled by upload later.`);
      setPendingAttachments(prev => [...prev, { ...newAttachmentBase }]);
    }
  };

  const removeAttachment = (attachmentId: string) => {
    setPendingAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const handleSendMessage = () => {
    if ((!message.trim() && pendingAttachments.length === 0) || disabled) return;

    if (topicId) {
      sendMessage(message, currentChannel?.id, undefined, threadId, topicId, pendingAttachments);
    } else {
      sendMessage(message, currentChannel?.id, currentDirectMessage?.id, threadId, undefined, pendingAttachments);
    }
    setMessage('');
    setPendingAttachments([]);
    editorInstanceRef.current?.codemirror.focus();
  };

  const handleEditorKeyDown = (_instance: any, event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      editorInstanceRef.current?.codemirror.getInputField().blur();
    }
  };

  const handlePaste = (_instance: any, event: ClipboardEvent) => {
    if (event.clipboardData) {
      for (let i = 0; i < event.clipboardData.items.length; i++) {
        const item = event.clipboardData.items[i];
        if (item.kind === 'file') {
          const file = item.getAsFile();
          if (file) {
            event.preventDefault();
            addFileAsAttachment(file);
          }
        }
      }
    }
  };
  
  const simpleMdeOptions: Options = useMemo(() => {
    return {
      autofocus: autoFocus,
      placeholder: disabled ? "Cannot send messages to an archived topic" : placeholder,
      toolbar: false, 
      status: false, 
      spellChecker: false,
      minHeight: "44px",
    };
  }, [autoFocus, placeholder, disabled]);

  useEffect(() => {
    if (autoFocus && editorInstanceRef.current) {
      setTimeout(() => editorInstanceRef.current?.codemirror.focus(), 0);
    }
  }, [autoFocus, currentChannel, currentDirectMessage]);

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (disabled) return;

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      Array.from(event.dataTransfer.files).forEach(file => {
        addFileAsAttachment(file);
      });
      event.dataTransfer.clearData();
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      Array.from(event.target.files).forEach(file => {
        addFileAsAttachment(file);
      });
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div 
      ref={dropZoneRef}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      className="flex flex-col border rounded-md bg-[var(--app-main-bg)] shadow-sm"
    >
      <div className={`message-input-editor-wrapper p-1 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
        <SimpleMDEEditor
          value={message}
          onChange={setMessage}
          options={simpleMdeOptions}
          getMdeInstance={(instance) => {
            editorInstanceRef.current = instance;
          }}
          events={{
            keydown: handleEditorKeyDown,
            paste: handlePaste,
          }}
        />
      </div>
      
      <div className="attachment-pills-area px-3 py-2 border-t border-[var(--app-border)] min-h-[40px] flex flex-wrap gap-2">
        {pendingAttachments.length === 0 && !disabled && (
          <div className="text-xs text-gray-400 dark:text-gray-500 py-1">
            Attach files by pasting, dragging, or using the '+' button.
          </div>
        )}
        {pendingAttachments.map(att => (
          <AttachmentPill key={att.id} attachment={att} onRemove={removeAttachment} />
        ))}
      </div>

      <div className="flex items-center p-2 border-t border-[var(--app-border)]">
        <input
          type="file"
          multiple
          ref={fileInputRef}
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,text/plain,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar" // Expanded acceptable file types
        />
        <button
          className={`p-2 rounded-full hover:bg-[var(--app-hover-bg)] transition-colors ${disabled ? 'text-[var(--app-main-text)] opacity-30 cursor-not-allowed' : 'text-[var(--app-main-text)]'}`}
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled}
          aria-label="Attach file"
        >
          <Plus size={20} />
        </button>
        <div className="flex-grow" />
        <button
          className={`p-2 rounded-full transition-colors ${
            (message.trim() || pendingAttachments.length > 0) && !disabled
              ? 'text-[var(--app-highlight)] hover:text-[var(--app-active)]'
              : 'text-[var(--app-main-text)] opacity-30'
          } ${disabled ? 'cursor-not-allowed' : ''}`}
          onClick={handleSendMessage}
          disabled={(!message.trim() && pendingAttachments.length === 0) || disabled}
          aria-label="Send message"
        >
          <Send size={20} />
        </button>
      </div>
    </div>
  );
};
